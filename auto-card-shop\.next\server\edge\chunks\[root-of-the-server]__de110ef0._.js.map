{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\nimport { getToken } from 'next-auth/jwt'\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // 保护管理员路由\n  if (pathname.startsWith('/admin')) {\n    const token = await getToken({ req: request })\n\n    if (!token) {\n      return NextResponse.redirect(new URL('/auth/signin', request.url))\n    }\n\n    if (token.role !== 'ADMIN') {\n      return NextResponse.redirect(new URL('/', request.url))\n    }\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,UAAU;IACV,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,MAAM,QAAQ,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;YAAE,KAAK;QAAQ;QAE5C,IAAI,CAAC,OAAO;YACV,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,QAAQ,GAAG;QAClE;QAEA,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;QACvD;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}