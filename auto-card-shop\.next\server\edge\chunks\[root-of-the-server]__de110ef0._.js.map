{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\nimport { getToken } from 'next-auth/jwt'\n\n\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  console.log(`[Middleware] 执行中间件: ${pathname}`)\n\n  // 跳过静态资源和API路由\n  if (\n    pathname.startsWith('/_next') ||\n    pathname.startsWith('/api') ||\n    pathname.startsWith('/favicon.ico') ||\n    pathname === '/maintenance'\n  ) {\n    return NextResponse.next()\n  }\n\n  // 保护管理员路由\n  if (pathname.startsWith('/admin')) {\n    const token = await getToken({ req: request })\n\n    if (!token) {\n      return NextResponse.redirect(new URL('/auth/signin', request.url))\n    }\n\n    if (token.role !== 'ADMIN') {\n      return NextResponse.redirect(new URL('/', request.url))\n    }\n\n    return NextResponse.next()\n  }\n\n  // 对于所有其他路径，重定向到维护页面（测试）\n  console.log(`[Middleware] 重定向到维护页面: ${pathname}`)\n  return NextResponse.redirect(new URL('/maintenance', request.url))\n}\n\nexport const config = {\n  matcher: '/(.*)',\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAIO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,UAAU;IAE7C,eAAe;IACf,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,WACpB,SAAS,UAAU,CAAC,mBACpB,aAAa,gBACb;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,UAAU;IACV,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,MAAM,QAAQ,MAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;YAAE,KAAK;QAAQ;QAE5C,IAAI,CAAC,OAAO;YACV,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,QAAQ,GAAG;QAClE;QAEA,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;QACvD;QAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,wBAAwB;IACxB,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;IAChD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,QAAQ,GAAG;AAClE;AAEO,MAAM,SAAS;IACpB,SAAS;AACX"}}]}