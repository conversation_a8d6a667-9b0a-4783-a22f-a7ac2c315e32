import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { startOfDay, endOfDay, startOfMonth, endOfMonth, subDays, subMonths, format } from 'date-fns'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: '未授权' }, { status: 401 })
    }

    const now = new Date()
    const today = startOfDay(now)
    const endToday = endOfDay(now)
    const thisMonth = startOfMonth(now)
    const endThisMonth = endOfMonth(now)
    const lastMonth = startOfMonth(subMonths(now, 1))
    const endLastMonth = endOfMonth(subMonths(now, 1))

    // 获取今日销售数据
    const todayOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: today,
          lte: endToday
        },
        status: 'DELIVERED'
      }
    })

    // 获取本月销售数据
    const thisMonthOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: thisMonth,
          lte: endThisMonth
        },
        status: 'DELIVERED'
      }
    })

    // 获取上月销售数据
    const lastMonthOrders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: lastMonth,
          lte: endLastMonth
        },
        status: 'DELIVERED'
      }
    })

    // 获取最近7天的销售趋势
    const salesTrend = []
    for (let i = 6; i >= 0; i--) {
      const date = subDays(now, i)
      const dayStart = startOfDay(date)
      const dayEnd = endOfDay(date)
      
      const dayOrders = await prisma.order.findMany({
        where: {
          createdAt: {
            gte: dayStart,
            lte: dayEnd
          },
          status: 'DELIVERED'
        }
      })

      salesTrend.push({
        date: format(date, 'MM-dd'),
        sales: dayOrders.reduce((sum, order) => sum + order.totalAmount, 0),
        orders: dayOrders.length
      })
    }

    // 获取热门商品 - 基于订单数量
    const popularProductsRaw = await prisma.orderItem.findMany({
      where: {
        order: {
          status: 'DELIVERED',
          createdAt: {
            gte: thisMonth
          }
        }
      },
      include: {
        product: {
          select: {
            name: true,
            image: true
          }
        }
      },
      take: 20 // 先取更多数据用于分组
    })

    // 手动分组统计
    const productStats = new Map()
    popularProductsRaw.forEach(item => {
      const productId = item.productId
      if (productStats.has(productId)) {
        const existing = productStats.get(productId)
        existing.count += item.quantity
        existing.totalAmount += item.price * item.quantity
      } else {
        productStats.set(productId, {
          productId,
          count: item.quantity,
          totalAmount: item.price * item.quantity,
          product: item.product
        })
      }
    })

    // 转换为数组并排序
    const popularProductsWithDetails = Array.from(productStats.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    // 获取收入统计
    const revenueStats = await prisma.order.aggregate({
      where: {
        status: 'DELIVERED'
      },
      _sum: {
        totalAmount: true
      },
      _count: {
        id: true
      }
    })

    // 计算统计数据
    const todayRevenue = todayOrders.reduce((sum, order) => sum + order.totalAmount, 0)
    const thisMonthRevenue = thisMonthOrders.reduce((sum, order) => sum + order.totalAmount, 0)
    const lastMonthRevenue = lastMonthOrders.reduce((sum, order) => sum + order.totalAmount, 0)
    
    const monthlyGrowth = lastMonthRevenue > 0 
      ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
      : 0

    // 获取用户增长数据
    const totalUsers = await prisma.user.count()
    const thisMonthUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: thisMonth
        }
      }
    })

    // 获取商品库存警告
    const products = await prisma.product.findMany({
      where: {
        status: 'ACTIVE'
      },
      select: {
        id: true,
        name: true
      }
    })

    const lowStockWarnings = []
    for (const product of products) {
      const availableCards = await prisma.card.count({
        where: {
          productId: product.id,
          status: 'AVAILABLE'
        }
      })

      if (availableCards < 10) {
        lowStockWarnings.push({
          id: product.id,
          name: product.name,
          stock: availableCards
        })
      }
    }

    return NextResponse.json({
      todayRevenue,
      thisMonthRevenue,
      lastMonthRevenue,
      monthlyGrowth,
      todayOrders: todayOrders.length,
      thisMonthOrders: thisMonthOrders.length,
      totalRevenue: revenueStats._sum.totalAmount || 0,
      totalOrders: revenueStats._count || 0,
      totalUsers,
      thisMonthUsers,
      salesTrend,
      popularProducts: popularProductsWithDetails,
      lowStockWarnings
    })

  } catch (error) {
    console.error('获取统计数据失败:', error)
    return NextResponse.json({ error: '获取统计数据失败' }, { status: 500 })
  }
}
