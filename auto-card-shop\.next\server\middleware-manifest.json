{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2ebd57a8._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_2c088799.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "W1guVrC+Px7EIJSfR0SnSL5iOnRJMCzuf2lPvgcuNgA=", "__NEXT_PREVIEW_MODE_ID": "520be7d67cbe828bb3cdb89b3fe6539a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9eca29751906aa517a2687289e9f65625c051e3c7f12da60a6b4787bfd9e75e9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4d89f8723fe4ada8d8b03522dee1426eb0222d598a41e4edcd32bbd127d89319"}}}, "sortedMiddleware": ["/"], "functions": {}}