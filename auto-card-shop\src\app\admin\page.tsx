'use client'

import { useEffect, useState } from 'react'
import { Package, ShoppingCart, CreditCard, Users, TrendingUp, Activity, BarChart3, DollarSign, Calendar, AlertTriangle, Star } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { SalesChart } from '@/components/charts/sales-chart'

interface Stats {
  totalProducts: number
  totalOrders: number
  totalCards: number
  totalUsers: number
  recentOrders: any[]
}

interface EnhancedStats {
  todayRevenue: number
  thisMonthRevenue: number
  lastMonthRevenue: number
  monthlyGrowth: number
  todayOrders: number
  thisMonthOrders: number
  totalRevenue: number
  totalOrders: number
  totalUsers: number
  thisMonthUsers: number
  salesTrend: Array<{
    date: string
    sales: number
    orders: number
  }>
  popularProducts: Array<{
    productId: string
    _count: { id: number }
    _sum: { totalAmount: number }
    product: { name: string; image: string | null }
  }>
  lowStockWarnings: Array<{
    id: string
    name: string
    stock: number
  }>
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats>({
    totalProducts: 0,
    totalOrders: 0,
    totalCards: 0,
    totalUsers: 0,
    recentOrders: []
  })
  const [enhancedStats, setEnhancedStats] = useState<EnhancedStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
    fetchEnhancedStats()
  }, [])

  const fetchStats = async () => {
    try {
      // 获取基础统计数据
      const [productsRes, ordersRes, cardsRes, usersRes] = await Promise.all([
        fetch('/api/products?status=all'),
        fetch('/api/orders'),
        fetch('/api/cards'),
        fetch('/api/admin/users')
      ])

      const products = await productsRes.json()
      const ordersData = await ordersRes.json()
      const cardsData = await cardsRes.json()
      const users = await usersRes.json()

      // Handle different response formats
      const ordersArray = Array.isArray(ordersData) ? ordersData : (ordersData.orders || [])
      const cardsArray = Array.isArray(cardsData) ? cardsData : (cardsData.cards || [])
      const productsArray = Array.isArray(products) ? products : []
      const usersArray = Array.isArray(users) ? users : []

      setStats({
        totalProducts: productsArray.length || 0,
        totalOrders: ordersArray.length || 0,
        totalCards: cardsArray.length || 0,
        totalUsers: usersArray.length || 0,
        recentOrders: ordersArray.slice(0, 5) || []
      })
    } catch (error) {
      console.error('获取基础统计数据失败:', error)
      setStats({
        totalProducts: 0,
        totalOrders: 0,
        totalCards: 0,
        totalUsers: 0,
        recentOrders: []
      })
    }
  }

  const fetchEnhancedStats = async () => {
    try {
      const response = await fetch('/api/admin/stats')
      if (response.ok) {
        const data = await response.json()
        setEnhancedStats(data)
      }
    } catch (error) {
      console.error('获取增强统计数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount)
  }

  const statCards = [
    {
      title: '今日收入',
      value: enhancedStats ? formatCurrency(enhancedStats.todayRevenue) : '¥0.00',
      subtitle: `${enhancedStats?.todayOrders || 0} 笔订单`,
      icon: DollarSign,
      color: 'bg-emerald-500',
      trend: enhancedStats?.todayRevenue > 0 ? '+' : ''
    },
    {
      title: '本月收入',
      value: enhancedStats ? formatCurrency(enhancedStats.thisMonthRevenue) : '¥0.00',
      subtitle: `${enhancedStats?.thisMonthOrders || 0} 笔订单`,
      icon: TrendingUp,
      color: 'bg-blue-500',
      trend: enhancedStats?.monthlyGrowth ? `${enhancedStats.monthlyGrowth > 0 ? '+' : ''}${enhancedStats.monthlyGrowth.toFixed(1)}%` : '0%'
    },
    {
      title: '总收入',
      value: enhancedStats ? formatCurrency(enhancedStats.totalRevenue) : '¥0.00',
      subtitle: `${enhancedStats?.totalOrders || 0} 笔订单`,
      icon: BarChart3,
      color: 'bg-purple-500',
      trend: '累计'
    },
    {
      title: '用户总数',
      value: enhancedStats?.totalUsers || stats.totalUsers,
      subtitle: `本月新增 ${enhancedStats?.thisMonthUsers || 0}`,
      icon: Users,
      color: 'bg-orange-500',
      trend: enhancedStats?.thisMonthUsers ? `+${enhancedStats.thisMonthUsers}` : '+0'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white gradient-bg">
          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          加载统计数据...
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="animate-fade-in">
        <h1 className="text-3xl font-bold gradient-text mb-2">仪表板</h1>
        <p className="text-gray-600 flex items-center">
          <Activity className="w-4 h-4 mr-2" />
          实时监控您的业务数据
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-up">
        {statCards.map((card, index) => {
          const Icon = card.icon
          return (
            <Card key={index} className="hover-lift animate-scale-in" style={{animationDelay: `${index * 0.1}s`}}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-muted-foreground mb-1">{card.title}</p>
                    <p className="text-2xl font-bold gradient-text mb-1">{card.value}</p>
                    <p className="text-xs text-gray-500">{card.subtitle}</p>
                  </div>
                  <div className={`${card.color} rounded-xl p-3 shadow-lg`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="mt-4 flex items-center justify-between">
                  <div className="flex items-center text-sm text-green-600">
                    <Activity className="w-4 h-4 mr-1" />
                    <span>实时数据</span>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {card.trend}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* 销售趋势图表 */}
      {enhancedStats && enhancedStats.salesTrend && enhancedStats.salesTrend.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-slide-up">
          {/* 销售趋势图 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-primary" />
                最近7天销售趋势
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <SalesChart data={enhancedStats.salesTrend} />
              </div>
            </CardContent>
          </Card>

          {/* 热门商品 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="w-5 h-5 mr-2 text-primary" />
                本月热门商品
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {enhancedStats.popularProducts && enhancedStats.popularProducts.length > 0 ? enhancedStats.popularProducts.slice(0, 5).map((item, index) => (
                  <div key={item.productId} className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center text-white text-sm font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <div className="text-sm font-semibold text-gray-900">
                          {item.product?.name || '未知商品'}
                        </div>
                        <div className="text-xs text-gray-500">
                          销量: {item._count?.id || 0} 件
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-bold gradient-text">
                        {formatCurrency(item._sum.totalAmount || 0)}
                      </div>
                    </div>
                  </div>
                )) : (
                  <div className="text-center py-8">
                    <Star className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">暂无热门商品数据</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 库存警告 */}
      {enhancedStats && enhancedStats.lowStockWarnings && enhancedStats.lowStockWarnings.length > 0 && (
        <Card className="animate-slide-up border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center text-orange-800">
              <AlertTriangle className="w-5 h-5 mr-2" />
              库存警告
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {enhancedStats.lowStockWarnings.map((product) => (
                <div key={product.id} className="flex items-center justify-between p-3 bg-white rounded-lg border border-orange-200">
                  <div>
                    <div className="text-sm font-semibold text-gray-900">{product.name}</div>
                    <div className="text-xs text-orange-600">库存不足</div>
                  </div>
                  <Badge variant="destructive" className="text-xs">
                    {product.stock} 张
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 快速操作 */}
      <Card className="animate-slide-up">
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-primary" />
            快速操作
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/admin/products"
              className="group p-6 border border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-lg transition-all duration-200 hover-lift"
            >
              <div className="flex items-center justify-between mb-3">
                <Package className="w-8 h-8 text-blue-500 group-hover:scale-110 transition-transform" />
                <Badge variant="outline">商品</Badge>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">管理商品</h3>
              <p className="text-sm text-gray-600">添加、编辑或删除商品</p>
            </a>

            <a
              href="/admin/cards"
              className="group p-6 border border-gray-200 rounded-xl hover:border-purple-300 hover:shadow-lg transition-all duration-200 hover-lift"
            >
              <div className="flex items-center justify-between mb-3">
                <CreditCard className="w-8 h-8 text-purple-500 group-hover:scale-110 transition-transform" />
                <Badge variant="outline">卡密</Badge>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">管理卡密</h3>
              <p className="text-sm text-gray-600">批量添加或查看卡密</p>
            </a>

            <a
              href="/admin/orders"
              className="group p-6 border border-gray-200 rounded-xl hover:border-green-300 hover:shadow-lg transition-all duration-200 hover-lift"
            >
              <div className="flex items-center justify-between mb-3">
                <ShoppingCart className="w-8 h-8 text-green-500 group-hover:scale-110 transition-transform" />
                <Badge variant="outline">订单</Badge>
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">查看订单</h3>
              <p className="text-sm text-gray-600">管理和处理订单</p>
            </a>
          </div>
        </CardContent>
      </Card>

      {/* 最近订单 */}
      {stats.recentOrders.length > 0 && (
        <Card className="animate-slide-up">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <ShoppingCart className="w-5 h-5 mr-2 text-primary" />
                最近订单
              </div>
              <Badge variant="gradient">{stats.recentOrders.length} 条</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentOrders.map((order: any, index: number) => (
                <div key={order.id || index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg hover:shadow-md transition-all duration-200 animate-fade-in" style={{animationDelay: `${index * 0.1}s`}}>
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {order.id ? order.id.slice(0, 2).toUpperCase() : 'N/A'}
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-gray-900">
                        订单 #{order.id ? order.id.slice(0, 8) : 'N/A'}...
                      </div>
                      <div className="text-xs text-gray-500">{order.email || '未知邮箱'}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold gradient-text">
                      ${order.totalAmount ? order.totalAmount.toFixed(2) : '0.00'}
                    </div>
                    <div className="text-xs text-gray-500">
                      {order.createdAt ? new Date(order.createdAt).toLocaleDateString('zh-CN') : '未知日期'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-6 text-center">
              <a
                href="/admin/orders"
                className="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
              >
                查看所有订单
                <TrendingUp className="w-4 h-4 ml-1" />
              </a>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 系统信息 */}
      <Card className="animate-slide-up">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2 text-primary" />
            系统信息
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                <span className="text-gray-600 font-medium">系统版本</span>
                <Badge variant="gradient">v1.0.0</Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                <span className="text-gray-600 font-medium">数据库</span>
                <Badge variant="success">SQLite</Badge>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
                <span className="text-gray-600 font-medium">支付网关</span>
                <Badge variant="info">Stripe</Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg">
                <span className="text-gray-600 font-medium">最后更新</span>
                <Badge variant="warning">{new Date().toLocaleString('zh-CN')}</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
