{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-elegant hover-lift\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 hover:bg-green-200\",\n        warning:\n          \"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n        info:\n          \"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200\",\n        gradient:\n          \"border-transparent gradient-bg text-white hover:opacity-80\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,UACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/charts/sales-chart.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport {\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  BarChart,\n  Bar,\n  Area,\n  AreaChart\n} from 'recharts'\n\ninterface SalesData {\n  date: string\n  sales: number\n  orders: number\n}\n\ninterface SalesChartProps {\n  data: SalesData[]\n  type?: 'line' | 'bar' | 'area'\n}\n\nexport function SalesChart({ data, type = 'area' }: SalesChartProps) {\n  const formatCurrency = (value: number) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value)\n  }\n\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      return (\n        <div className=\"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\">\n          <p className=\"text-sm font-medium text-gray-900 mb-2\">{`日期: ${label}`}</p>\n          <p className=\"text-sm text-blue-600\">\n            {`销售额: ${formatCurrency(payload[0].value)}`}\n          </p>\n          <p className=\"text-sm text-green-600\">\n            {`订单数: ${payload[1]?.value || 0} 笔`}\n          </p>\n        </div>\n      )\n    }\n    return null\n  }\n\n  if (type === 'line') {\n    return (\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>\n          <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n          <XAxis \n            dataKey=\"date\" \n            stroke=\"#666\"\n            fontSize={12}\n            tickLine={false}\n            axisLine={false}\n          />\n          <YAxis \n            stroke=\"#666\"\n            fontSize={12}\n            tickLine={false}\n            axisLine={false}\n            tickFormatter={formatCurrency}\n          />\n          <Tooltip content={<CustomTooltip />} />\n          <Line \n            type=\"monotone\" \n            dataKey=\"sales\" \n            stroke=\"#3b82f6\" \n            strokeWidth={3}\n            dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}\n            activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}\n          />\n        </LineChart>\n      </ResponsiveContainer>\n    )\n  }\n\n  if (type === 'bar') {\n    return (\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>\n          <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n          <XAxis \n            dataKey=\"date\" \n            stroke=\"#666\"\n            fontSize={12}\n            tickLine={false}\n            axisLine={false}\n          />\n          <YAxis \n            stroke=\"#666\"\n            fontSize={12}\n            tickLine={false}\n            axisLine={false}\n            tickFormatter={formatCurrency}\n          />\n          <Tooltip content={<CustomTooltip />} />\n          <Bar \n            dataKey=\"sales\" \n            fill=\"#3b82f6\"\n            radius={[4, 4, 0, 0]}\n          />\n        </BarChart>\n      </ResponsiveContainer>\n    )\n  }\n\n  // 默认使用面积图\n  return (\n    <ResponsiveContainer width=\"100%\" height=\"100%\">\n      <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>\n        <defs>\n          <linearGradient id=\"salesGradient\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n            <stop offset=\"5%\" stopColor=\"#3b82f6\" stopOpacity={0.3}/>\n            <stop offset=\"95%\" stopColor=\"#3b82f6\" stopOpacity={0.05}/>\n          </linearGradient>\n        </defs>\n        <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n        <XAxis \n          dataKey=\"date\" \n          stroke=\"#666\"\n          fontSize={12}\n          tickLine={false}\n          axisLine={false}\n        />\n        <YAxis \n          stroke=\"#666\"\n          fontSize={12}\n          tickLine={false}\n          axisLine={false}\n          tickFormatter={formatCurrency}\n        />\n        <Tooltip content={<CustomTooltip />} />\n        <Area \n          type=\"monotone\" \n          dataKey=\"sales\" \n          stroke=\"#3b82f6\" \n          strokeWidth={2}\n          fill=\"url(#salesGradient)\"\n          dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}\n          activeDot={{ r: 5, stroke: '#3b82f6', strokeWidth: 2 }}\n        />\n      </AreaChart>\n    </ResponsiveContainer>\n  )\n}\n\n// 订单数量图表\nexport function OrdersChart({ data }: { data: SalesData[] }) {\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      return (\n        <div className=\"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\">\n          <p className=\"text-sm font-medium text-gray-900 mb-2\">{`日期: ${label}`}</p>\n          <p className=\"text-sm text-green-600\">\n            {`订单数: ${payload[0].value} 笔`}\n          </p>\n        </div>\n      )\n    }\n    return null\n  }\n\n  return (\n    <ResponsiveContainer width=\"100%\" height=\"100%\">\n      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>\n        <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n        <XAxis \n          dataKey=\"date\" \n          stroke=\"#666\"\n          fontSize={12}\n          tickLine={false}\n          axisLine={false}\n        />\n        <YAxis \n          stroke=\"#666\"\n          fontSize={12}\n          tickLine={false}\n          axisLine={false}\n        />\n        <Tooltip content={<CustomTooltip />} />\n        <Bar \n          dataKey=\"orders\" \n          fill=\"#10b981\"\n          radius={[4, 4, 0, 0]}\n        />\n      </BarChart>\n    </ResponsiveContainer>\n  )\n}\n\n// 组合图表（销售额 + 订单数）\nexport function CombinedChart({ data }: { data: SalesData[] }) {\n  const formatCurrency = (value: number) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value)\n  }\n\n  const CustomTooltip = ({ active, payload, label }: any) => {\n    if (active && payload && payload.length) {\n      return (\n        <div className=\"bg-white p-3 border border-gray-200 rounded-lg shadow-lg\">\n          <p className=\"text-sm font-medium text-gray-900 mb-2\">{`日期: ${label}`}</p>\n          {payload.map((entry: any, index: number) => (\n            <p key={index} className=\"text-sm\" style={{ color: entry.color }}>\n              {entry.dataKey === 'sales' \n                ? `销售额: ${formatCurrency(entry.value)}`\n                : `订单数: ${entry.value} 笔`\n              }\n            </p>\n          ))}\n        </div>\n      )\n    }\n    return null\n  }\n\n  return (\n    <ResponsiveContainer width=\"100%\" height=\"100%\">\n      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>\n        <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\n        <XAxis \n          dataKey=\"date\" \n          stroke=\"#666\"\n          fontSize={12}\n          tickLine={false}\n          axisLine={false}\n        />\n        <YAxis \n          yAxisId=\"sales\"\n          stroke=\"#666\"\n          fontSize={12}\n          tickLine={false}\n          axisLine={false}\n          tickFormatter={formatCurrency}\n        />\n        <YAxis \n          yAxisId=\"orders\"\n          orientation=\"right\"\n          stroke=\"#666\"\n          fontSize={12}\n          tickLine={false}\n          axisLine={false}\n        />\n        <Tooltip content={<CustomTooltip />} />\n        <Line \n          yAxisId=\"sales\"\n          type=\"monotone\" \n          dataKey=\"sales\" \n          stroke=\"#3b82f6\" \n          strokeWidth={3}\n          dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}\n        />\n        <Line \n          yAxisId=\"orders\"\n          type=\"monotone\" \n          dataKey=\"orders\" \n          stroke=\"#10b981\" \n          strokeWidth={3}\n          dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}\n        />\n      </LineChart>\n    </ResponsiveContainer>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;AA4BO,SAAS,WAAW,EAAE,IAAI,EAAE,OAAO,MAAM,EAAmB;IACjE,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA0C,CAAC,IAAI,EAAE,OAAO;;;;;;kCACrE,8OAAC;wBAAE,WAAU;kCACV,CAAC,KAAK,EAAE,eAAe,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG;;;;;;kCAE7C,8OAAC;wBAAE,WAAU;kCACV,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC;;;;;;;;;;;;QAI3C;QACA,OAAO;IACT;IAEA,IAAI,SAAS,QAAQ;QACnB,qBACE,8OAAC,mKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;gBAAC,MAAM;gBAAM,QAAQ;oBAAE,KAAK;oBAAG,OAAO;oBAAI,MAAM;oBAAI,QAAQ;gBAAE;;kCACtE,8OAAC,6JAAA,CAAA,gBAAa;wBAAC,iBAAgB;wBAAM,QAAO;;;;;;kCAC5C,8OAAC,qJAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,UAAU;wBACV,UAAU;;;;;;kCAEZ,8OAAC,qJAAA,CAAA,QAAK;wBACJ,QAAO;wBACP,UAAU;wBACV,UAAU;wBACV,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,uJAAA,CAAA,UAAO;wBAAC,uBAAS,8OAAC;;;;;;;;;;kCACnB,8OAAC,oJAAA,CAAA,OAAI;wBACH,MAAK;wBACL,SAAQ;wBACR,QAAO;wBACP,aAAa;wBACb,KAAK;4BAAE,MAAM;4BAAW,aAAa;4BAAG,GAAG;wBAAE;wBAC7C,WAAW;4BAAE,GAAG;4BAAG,QAAQ;4BAAW,aAAa;wBAAE;;;;;;;;;;;;;;;;;IAK/D;IAEA,IAAI,SAAS,OAAO;QAClB,qBACE,8OAAC,mKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;gBAAC,MAAM;gBAAM,QAAQ;oBAAE,KAAK;oBAAG,OAAO;oBAAI,MAAM;oBAAI,QAAQ;gBAAE;;kCACrE,8OAAC,6JAAA,CAAA,gBAAa;wBAAC,iBAAgB;wBAAM,QAAO;;;;;;kCAC5C,8OAAC,qJAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,UAAU;wBACV,UAAU;;;;;;kCAEZ,8OAAC,qJAAA,CAAA,QAAK;wBACJ,QAAO;wBACP,UAAU;wBACV,UAAU;wBACV,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,uJAAA,CAAA,UAAO;wBAAC,uBAAS,8OAAC;;;;;;;;;;kCACnB,8OAAC,mJAAA,CAAA,MAAG;wBACF,SAAQ;wBACR,MAAK;wBACL,QAAQ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;;;;;;;;;;;;;;;;;IAK9B;IAEA,UAAU;IACV,qBACE,8OAAC,mKAAA,CAAA,sBAAmB;QAAC,OAAM;QAAO,QAAO;kBACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;YAAC,MAAM;YAAM,QAAQ;gBAAE,KAAK;gBAAG,OAAO;gBAAI,MAAM;gBAAI,QAAQ;YAAE;;8BACtE,8OAAC;8BACC,cAAA,8OAAC;wBAAe,IAAG;wBAAgB,IAAG;wBAAI,IAAG;wBAAI,IAAG;wBAAI,IAAG;;0CACzD,8OAAC;gCAAK,QAAO;gCAAK,WAAU;gCAAU,aAAa;;;;;;0CACnD,8OAAC;gCAAK,QAAO;gCAAM,WAAU;gCAAU,aAAa;;;;;;;;;;;;;;;;;8BAGxD,8OAAC,6JAAA,CAAA,gBAAa;oBAAC,iBAAgB;oBAAM,QAAO;;;;;;8BAC5C,8OAAC,qJAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,QAAO;oBACP,UAAU;oBACV,UAAU;oBACV,UAAU;;;;;;8BAEZ,8OAAC,qJAAA,CAAA,QAAK;oBACJ,QAAO;oBACP,UAAU;oBACV,UAAU;oBACV,UAAU;oBACV,eAAe;;;;;;8BAEjB,8OAAC,uJAAA,CAAA,UAAO;oBAAC,uBAAS,8OAAC;;;;;;;;;;8BACnB,8OAAC,oJAAA,CAAA,OAAI;oBACH,MAAK;oBACL,SAAQ;oBACR,QAAO;oBACP,aAAa;oBACb,MAAK;oBACL,KAAK;wBAAE,MAAM;wBAAW,aAAa;wBAAG,GAAG;oBAAE;oBAC7C,WAAW;wBAAE,GAAG;wBAAG,QAAQ;wBAAW,aAAa;oBAAE;;;;;;;;;;;;;;;;;AAK/D;AAGO,SAAS,YAAY,EAAE,IAAI,EAAyB;IACzD,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA0C,CAAC,IAAI,EAAE,OAAO;;;;;;kCACrE,8OAAC;wBAAE,WAAU;kCACV,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;;;;;;;;;;;;QAIrC;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,mKAAA,CAAA,sBAAmB;QAAC,OAAM;QAAO,QAAO;kBACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;YAAC,MAAM;YAAM,QAAQ;gBAAE,KAAK;gBAAG,OAAO;gBAAI,MAAM;gBAAI,QAAQ;YAAE;;8BACrE,8OAAC,6JAAA,CAAA,gBAAa;oBAAC,iBAAgB;oBAAM,QAAO;;;;;;8BAC5C,8OAAC,qJAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,QAAO;oBACP,UAAU;oBACV,UAAU;oBACV,UAAU;;;;;;8BAEZ,8OAAC,qJAAA,CAAA,QAAK;oBACJ,QAAO;oBACP,UAAU;oBACV,UAAU;oBACV,UAAU;;;;;;8BAEZ,8OAAC,uJAAA,CAAA,UAAO;oBAAC,uBAAS,8OAAC;;;;;;;;;;8BACnB,8OAAC,mJAAA,CAAA,MAAG;oBACF,SAAQ;oBACR,MAAK;oBACL,QAAQ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE;;;;;;;;;;;;;;;;;AAK9B;AAGO,SAAS,cAAc,EAAE,IAAI,EAAyB;IAC3D,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA0C,CAAC,IAAI,EAAE,OAAO;;;;;;oBACpE,QAAQ,GAAG,CAAC,CAAC,OAAY,sBACxB,8OAAC;4BAAc,WAAU;4BAAU,OAAO;gCAAE,OAAO,MAAM,KAAK;4BAAC;sCAC5D,MAAM,OAAO,KAAK,UACf,CAAC,KAAK,EAAE,eAAe,MAAM,KAAK,GAAG,GACrC,CAAC,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;2BAHrB;;;;;;;;;;;QAShB;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,mKAAA,CAAA,sBAAmB;QAAC,OAAM;QAAO,QAAO;kBACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;YAAC,MAAM;YAAM,QAAQ;gBAAE,KAAK;gBAAG,OAAO;gBAAI,MAAM;gBAAI,QAAQ;YAAE;;8BACtE,8OAAC,6JAAA,CAAA,gBAAa;oBAAC,iBAAgB;oBAAM,QAAO;;;;;;8BAC5C,8OAAC,qJAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,QAAO;oBACP,UAAU;oBACV,UAAU;oBACV,UAAU;;;;;;8BAEZ,8OAAC,qJAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,QAAO;oBACP,UAAU;oBACV,UAAU;oBACV,UAAU;oBACV,eAAe;;;;;;8BAEjB,8OAAC,qJAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,aAAY;oBACZ,QAAO;oBACP,UAAU;oBACV,UAAU;oBACV,UAAU;;;;;;8BAEZ,8OAAC,uJAAA,CAAA,UAAO;oBAAC,uBAAS,8OAAC;;;;;;;;;;8BACnB,8OAAC,oJAAA,CAAA,OAAI;oBACH,SAAQ;oBACR,MAAK;oBACL,SAAQ;oBACR,QAAO;oBACP,aAAa;oBACb,KAAK;wBAAE,MAAM;wBAAW,aAAa;wBAAG,GAAG;oBAAE;;;;;;8BAE/C,8OAAC,oJAAA,CAAA,OAAI;oBACH,SAAQ;oBACR,MAAK;oBACL,SAAQ;oBACR,QAAO;oBACP,aAAa;oBACb,KAAK;wBAAE,MAAM;wBAAW,aAAa;wBAAG,GAAG;oBAAE;;;;;;;;;;;;;;;;;AAKvD", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { Package, ShoppingCart, CreditCard, Users, TrendingUp, Activity, BarChart3, DollarSign, Calendar, AlertTriangle, Star } from 'lucide-react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { SalesChart } from '@/components/charts/sales-chart'\n\ninterface Stats {\n  totalProducts: number\n  totalOrders: number\n  totalCards: number\n  totalUsers: number\n  recentOrders: any[]\n}\n\ninterface EnhancedStats {\n  todayRevenue: number\n  thisMonthRevenue: number\n  lastMonthRevenue: number\n  monthlyGrowth: number\n  todayOrders: number\n  thisMonthOrders: number\n  totalRevenue: number\n  totalOrders: number\n  totalUsers: number\n  thisMonthUsers: number\n  salesTrend: Array<{\n    date: string\n    sales: number\n    orders: number\n  }>\n  popularProducts: Array<{\n    productId: string\n    _count: { id: number }\n    _sum: { totalAmount: number }\n    product: { name: string; image: string | null }\n  }>\n  lowStockWarnings: Array<{\n    id: string\n    name: string\n    stock: number\n  }>\n}\n\nexport default function AdminDashboard() {\n  const [stats, setStats] = useState<Stats>({\n    totalProducts: 0,\n    totalOrders: 0,\n    totalCards: 0,\n    totalUsers: 0,\n    recentOrders: []\n  })\n  const [enhancedStats, setEnhancedStats] = useState<EnhancedStats | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    fetchStats()\n    fetchEnhancedStats()\n  }, [])\n\n  const fetchStats = async () => {\n    try {\n      // 获取基础统计数据\n      const [productsRes, ordersRes, cardsRes, usersRes] = await Promise.all([\n        fetch('/api/products?status=all'),\n        fetch('/api/orders'),\n        fetch('/api/cards'),\n        fetch('/api/admin/users')\n      ])\n\n      const products = await productsRes.json()\n      const orders = await ordersRes.json()\n      const cards = await cardsRes.json()\n      const users = await usersRes.json()\n\n      setStats({\n        totalProducts: products.length || 0,\n        totalOrders: orders.length || 0,\n        totalCards: cards.length || 0,\n        totalUsers: users.length || 0,\n        recentOrders: orders.slice(0, 5) || []\n      })\n    } catch (error) {\n      console.error('获取基础统计数据失败:', error)\n      setStats({\n        totalProducts: 0,\n        totalOrders: 0,\n        totalCards: 0,\n        totalUsers: 0,\n        recentOrders: []\n      })\n    }\n  }\n\n  const fetchEnhancedStats = async () => {\n    try {\n      const response = await fetch('/api/admin/stats')\n      if (response.ok) {\n        const data = await response.json()\n        setEnhancedStats(data)\n      }\n    } catch (error) {\n      console.error('获取增强统计数据失败:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'CNY'\n    }).format(amount)\n  }\n\n  const statCards = [\n    {\n      title: '今日收入',\n      value: enhancedStats ? formatCurrency(enhancedStats.todayRevenue) : '¥0.00',\n      subtitle: `${enhancedStats?.todayOrders || 0} 笔订单`,\n      icon: DollarSign,\n      color: 'bg-emerald-500',\n      trend: enhancedStats?.todayRevenue > 0 ? '+' : ''\n    },\n    {\n      title: '本月收入',\n      value: enhancedStats ? formatCurrency(enhancedStats.thisMonthRevenue) : '¥0.00',\n      subtitle: `${enhancedStats?.thisMonthOrders || 0} 笔订单`,\n      icon: TrendingUp,\n      color: 'bg-blue-500',\n      trend: enhancedStats?.monthlyGrowth ? `${enhancedStats.monthlyGrowth > 0 ? '+' : ''}${enhancedStats.monthlyGrowth.toFixed(1)}%` : '0%'\n    },\n    {\n      title: '总收入',\n      value: enhancedStats ? formatCurrency(enhancedStats.totalRevenue) : '¥0.00',\n      subtitle: `${enhancedStats?.totalOrders || 0} 笔订单`,\n      icon: BarChart3,\n      color: 'bg-purple-500',\n      trend: '累计'\n    },\n    {\n      title: '用户总数',\n      value: enhancedStats?.totalUsers || stats.totalUsers,\n      subtitle: `本月新增 ${enhancedStats?.thisMonthUsers || 0}`,\n      icon: Users,\n      color: 'bg-orange-500',\n      trend: enhancedStats?.thisMonthUsers ? `+${enhancedStats.thisMonthUsers}` : '+0'\n    }\n  ]\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white gradient-bg\">\n          <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          加载统计数据...\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"animate-fade-in\">\n        <h1 className=\"text-3xl font-bold gradient-text mb-2\">仪表板</h1>\n        <p className=\"text-gray-600 flex items-center\">\n          <Activity className=\"w-4 h-4 mr-2\" />\n          实时监控您的业务数据\n        </p>\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-up\">\n        {statCards.map((card, index) => {\n          const Icon = card.icon\n          return (\n            <Card key={index} className=\"hover-lift animate-scale-in\" style={{animationDelay: `${index * 0.1}s`}}>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium text-muted-foreground mb-1\">{card.title}</p>\n                    <p className=\"text-2xl font-bold gradient-text mb-1\">{card.value}</p>\n                    <p className=\"text-xs text-gray-500\">{card.subtitle}</p>\n                  </div>\n                  <div className={`${card.color} rounded-xl p-3 shadow-lg`}>\n                    <Icon className=\"w-6 h-6 text-white\" />\n                  </div>\n                </div>\n                <div className=\"mt-4 flex items-center justify-between\">\n                  <div className=\"flex items-center text-sm text-green-600\">\n                    <Activity className=\"w-4 h-4 mr-1\" />\n                    <span>实时数据</span>\n                  </div>\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    {card.trend}\n                  </Badge>\n                </div>\n              </CardContent>\n            </Card>\n          )\n        })}\n      </div>\n\n      {/* 销售趋势图表 */}\n      {enhancedStats && enhancedStats.salesTrend.length > 0 && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 animate-slide-up\">\n          {/* 销售趋势图 */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <TrendingUp className=\"w-5 h-5 mr-2 text-primary\" />\n                最近7天销售趋势\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"h-64\">\n                <SalesChart data={enhancedStats.salesTrend} />\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* 热门商品 */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Star className=\"w-5 h-5 mr-2 text-primary\" />\n                本月热门商品\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {enhancedStats.popularProducts.slice(0, 5).map((item, index) => (\n                  <div key={item.productId} className=\"flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                        {index + 1}\n                      </div>\n                      <div>\n                        <div className=\"text-sm font-semibold text-gray-900\">\n                          {item.product?.name || '未知商品'}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">\n                          销量: {item._count.id} 件\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm font-bold gradient-text\">\n                        {formatCurrency(item._sum.totalAmount || 0)}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* 库存警告 */}\n      {enhancedStats && enhancedStats.lowStockWarnings.length > 0 && (\n        <Card className=\"animate-slide-up border-orange-200 bg-orange-50\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center text-orange-800\">\n              <AlertTriangle className=\"w-5 h-5 mr-2\" />\n              库存警告\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {enhancedStats.lowStockWarnings.map((product) => (\n                <div key={product.id} className=\"flex items-center justify-between p-3 bg-white rounded-lg border border-orange-200\">\n                  <div>\n                    <div className=\"text-sm font-semibold text-gray-900\">{product.name}</div>\n                    <div className=\"text-xs text-orange-600\">库存不足</div>\n                  </div>\n                  <Badge variant=\"destructive\" className=\"text-xs\">\n                    {product.stock} 张\n                  </Badge>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* 快速操作 */}\n      <Card className=\"animate-slide-up\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <BarChart3 className=\"w-5 h-5 mr-2 text-primary\" />\n            快速操作\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <a\n              href=\"/admin/products\"\n              className=\"group p-6 border border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-lg transition-all duration-200 hover-lift\"\n            >\n              <div className=\"flex items-center justify-between mb-3\">\n                <Package className=\"w-8 h-8 text-blue-500 group-hover:scale-110 transition-transform\" />\n                <Badge variant=\"outline\">商品</Badge>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-1\">管理商品</h3>\n              <p className=\"text-sm text-gray-600\">添加、编辑或删除商品</p>\n            </a>\n\n            <a\n              href=\"/admin/cards\"\n              className=\"group p-6 border border-gray-200 rounded-xl hover:border-purple-300 hover:shadow-lg transition-all duration-200 hover-lift\"\n            >\n              <div className=\"flex items-center justify-between mb-3\">\n                <CreditCard className=\"w-8 h-8 text-purple-500 group-hover:scale-110 transition-transform\" />\n                <Badge variant=\"outline\">卡密</Badge>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-1\">管理卡密</h3>\n              <p className=\"text-sm text-gray-600\">批量添加或查看卡密</p>\n            </a>\n\n            <a\n              href=\"/admin/orders\"\n              className=\"group p-6 border border-gray-200 rounded-xl hover:border-green-300 hover:shadow-lg transition-all duration-200 hover-lift\"\n            >\n              <div className=\"flex items-center justify-between mb-3\">\n                <ShoppingCart className=\"w-8 h-8 text-green-500 group-hover:scale-110 transition-transform\" />\n                <Badge variant=\"outline\">订单</Badge>\n              </div>\n              <h3 className=\"font-semibold text-gray-900 mb-1\">查看订单</h3>\n              <p className=\"text-sm text-gray-600\">管理和处理订单</p>\n            </a>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 最近订单 */}\n      {stats.recentOrders.length > 0 && (\n        <Card className=\"animate-slide-up\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <ShoppingCart className=\"w-5 h-5 mr-2 text-primary\" />\n                最近订单\n              </div>\n              <Badge variant=\"gradient\">{stats.recentOrders.length} 条</Badge>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {stats.recentOrders.map((order: any, index: number) => (\n                <div key={order.id} className=\"flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg hover:shadow-md transition-all duration-200 animate-fade-in\" style={{animationDelay: `${index * 0.1}s`}}>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                      {order.id.slice(0, 2).toUpperCase()}\n                    </div>\n                    <div>\n                      <div className=\"text-sm font-semibold text-gray-900\">\n                        订单 #{order.id.slice(0, 8)}...\n                      </div>\n                      <div className=\"text-xs text-gray-500\">{order.email}</div>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-bold gradient-text\">\n                      ${order.totalAmount.toFixed(2)}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      {new Date(order.createdAt).toLocaleDateString('zh-CN')}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"mt-6 text-center\">\n              <a\n                href=\"/admin/orders\"\n                className=\"inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors\"\n              >\n                查看所有订单\n                <TrendingUp className=\"w-4 h-4 ml-1\" />\n              </a>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* 系统信息 */}\n      <Card className=\"animate-slide-up\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Activity className=\"w-5 h-5 mr-2 text-primary\" />\n            系统信息\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg\">\n                <span className=\"text-gray-600 font-medium\">系统版本</span>\n                <Badge variant=\"gradient\">v1.0.0</Badge>\n              </div>\n              <div className=\"flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg\">\n                <span className=\"text-gray-600 font-medium\">数据库</span>\n                <Badge variant=\"success\">SQLite</Badge>\n              </div>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg\">\n                <span className=\"text-gray-600 font-medium\">支付网关</span>\n                <Badge variant=\"info\">Stripe</Badge>\n              </div>\n              <div className=\"flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg\">\n                <span className=\"text-gray-600 font-medium\">最后更新</span>\n                <Badge variant=\"warning\">{new Date().toLocaleString('zh-CN')}</Badge>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AA6Ce,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;QACxC,eAAe;QACf,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,cAAc,EAAE;IAClB;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,CAAC,aAAa,WAAW,UAAU,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;aACP;YAED,MAAM,WAAW,MAAM,YAAY,IAAI;YACvC,MAAM,SAAS,MAAM,UAAU,IAAI;YACnC,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,QAAQ,MAAM,SAAS,IAAI;YAEjC,SAAS;gBACP,eAAe,SAAS,MAAM,IAAI;gBAClC,aAAa,OAAO,MAAM,IAAI;gBAC9B,YAAY,MAAM,MAAM,IAAI;gBAC5B,YAAY,MAAM,MAAM,IAAI;gBAC5B,cAAc,OAAO,KAAK,CAAC,GAAG,MAAM,EAAE;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,SAAS;gBACP,eAAe;gBACf,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc,EAAE;YAClB;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC/B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,gBAAgB,eAAe,cAAc,YAAY,IAAI;YACpE,UAAU,GAAG,eAAe,eAAe,EAAE,IAAI,CAAC;YAClD,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,OAAO,eAAe,eAAe,IAAI,MAAM;QACjD;QACA;YACE,OAAO;YACP,OAAO,gBAAgB,eAAe,cAAc,gBAAgB,IAAI;YACxE,UAAU,GAAG,eAAe,mBAAmB,EAAE,IAAI,CAAC;YACtD,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,OAAO,eAAe,gBAAgB,GAAG,cAAc,aAAa,GAAG,IAAI,MAAM,KAAK,cAAc,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;QACpI;QACA;YACE,OAAO;YACP,OAAO,gBAAgB,eAAe,cAAc,YAAY,IAAI;YACpE,UAAU,GAAG,eAAe,eAAe,EAAE,IAAI,CAAC;YAClD,MAAM,kNAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,eAAe,cAAc,MAAM,UAAU;YACpD,UAAU,CAAC,KAAK,EAAE,eAAe,kBAAkB,GAAG;YACtD,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO,eAAe,iBAAiB,CAAC,CAAC,EAAE,cAAc,cAAc,EAAE,GAAG;QAC9E;KACD;IAED,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA6C,OAAM;wBAA6B,MAAK;wBAAO,SAAQ;;0CACjH,8OAAC;gCAAO,WAAU;gCAAa,IAAG;gCAAK,IAAG;gCAAK,GAAE;gCAAK,QAAO;gCAAe,aAAY;;;;;;0CACxF,8OAAC;gCAAK,WAAU;gCAAa,MAAK;gCAAe,GAAE;;;;;;;;;;;;oBAC/C;;;;;;;;;;;;IAKd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;;0CACX,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM;oBACpB,MAAM,OAAO,KAAK,IAAI;oBACtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;wBAAa,WAAU;wBAA8B,OAAO;4BAAC,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;wBAAA;kCACjG,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAkD,KAAK,KAAK;;;;;;8DACzE,8OAAC;oDAAE,WAAU;8DAAyC,KAAK,KAAK;;;;;;8DAChE,8OAAC;oDAAE,WAAU;8DAAyB,KAAK,QAAQ;;;;;;;;;;;;sDAErD,8OAAC;4CAAI,WAAW,GAAG,KAAK,KAAK,CAAC,yBAAyB,CAAC;sDACtD,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;;8CAGpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAChC,KAAK,KAAK;;;;;;;;;;;;;;;;;;uBAlBR;;;;;gBAwBf;;;;;;YAID,iBAAiB,cAAc,UAAU,CAAC,MAAM,GAAG,mBAClD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAA8B;;;;;;;;;;;;0CAIxD,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8IAAA,CAAA,aAAU;wCAAC,MAAM,cAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;kCAMhD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAA8B;;;;;;;;;;;;0CAIlD,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,cAAc,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpD,8OAAC;4CAAyB,WAAU;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;sEAEX,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EACZ,KAAK,OAAO,EAAE,QAAQ;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;wEAAwB;wEAChC,KAAK,MAAM,CAAC,EAAE;wEAAC;;;;;;;;;;;;;;;;;;;8DAI1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,eAAe,KAAK,IAAI,CAAC,WAAW,IAAI;;;;;;;;;;;;2CAhBrC,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;YA4BnC,iBAAiB,cAAc,gBAAgB,CAAC,MAAM,GAAG,mBACxD,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI9C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,cAAc,gBAAgB,CAAC,GAAG,CAAC,CAAC,wBACnC,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAuC,QAAQ,IAAI;;;;;;8DAClE,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;sDAE3C,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;;gDACpC,QAAQ,KAAK;gDAAC;;;;;;;;mCANT,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BAgB9B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;;;;;;kCAIvD,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO5C,MAAM,YAAY,CAAC,MAAM,GAAG,mBAC3B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAA8B;;;;;;;8CAGxD,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAY,MAAM,YAAY,CAAC,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAGzD,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CACZ,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,OAAY,sBACnC,8OAAC;wCAAmB,WAAU;wCAAwJ,OAAO;4CAAC,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;wCAAA;;0DAC7N,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;kEAEnC,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;oEAAsC;oEAC9C,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG;oEAAG;;;;;;;0EAE5B,8OAAC;gEAAI,WAAU;0EAAyB,MAAM,KAAK;;;;;;;;;;;;;;;;;;0DAGvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DAAkC;4DAC7C,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;kEAE9B,8OAAC;wDAAI,WAAU;kEACZ,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;uCAjB1C,MAAM,EAAE;;;;;;;;;;0CAuBtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;;;;;;kCAItD,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;;8CAG7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;;;;;;;sDAExB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW,IAAI,OAAO,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpE", "debugId": null}}]}