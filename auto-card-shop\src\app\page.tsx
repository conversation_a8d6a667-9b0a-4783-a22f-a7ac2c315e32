'use client'

import { useEffect, useMemo, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ContentPreview } from '@/components/ui/content-renderer'
import { formatPrice } from '@/lib/utils'
import { ShoppingCart, User, LogIn, Package, Search, SortAsc, SortDesc, X } from 'lucide-react'

interface Product {
  id: string
  name: string
  description: string
  price: number
  image: string
  category: {
    id: string
    name: string
  }
  _count: {
    cards: number
  }
}

interface Category {
  id: string
  name: string
  slug: string
  _count: {
    products: number
  }
}

export default function Home() {
  const { data: session } = useSession()
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'default' | 'price-asc' | 'price-desc'>('default')

  useEffect(() => {
    fetchCategories()
    fetchProducts()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  const fetchProducts = async (categoryId?: string) => {
    try {
      const url = categoryId
        ? `/api/products?categoryId=${categoryId}`
        : '/api/products'
      const response = await fetch(url)
      const data = await response.json()
      setProducts(data)
    } catch (error) {
      console.error('获取商品失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId)
    setLoading(true)
    fetchProducts(categoryId || undefined)
  }

  const handleBuyNow = (productId: string) => {
    router.push(`/checkout?productId=${productId}&quantity=1`)
  }

  // 过滤和排序商品
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products

    // 按分类过滤
    if (selectedCategory) {
      filtered = filtered.filter((product: Product) => product.category.id === selectedCategory)
    }

    // 按搜索词过滤
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchLower) ||
        product.category.name.toLowerCase().includes(searchLower)
      )
    }

    // 排序
    if (sortBy === 'price-asc') {
      filtered = [...filtered].sort((a, b) => a.price - b.price)
    } else if (sortBy === 'price-desc') {
      filtered = [...filtered].sort((a, b) => b.price - a.price)
    }

    return filtered
  }, [products, selectedCategory, searchTerm, sortBy])

  return (
    <div className="min-h-screen bg-white">
      {/* 导航栏 */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                自动发卡网站
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Link href="/orders" className="text-gray-700 hover:text-gray-900 transition-colors">
                  订单查询
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 主内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 简约标题 */}
        <div className="mb-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            自动发卡商城
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            安全、快速、便捷的数字商品购买体验
          </p>
        </div>

        {/* 搜索和筛选 */}
        <div className="mb-8 space-y-4">
          {/* 搜索框和排序 */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="搜索商品名称或分类..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* 排序选择 */}
            <div className="flex items-center space-x-2 min-w-0">
              <span className="text-sm text-gray-700 whitespace-nowrap">排序:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'default' | 'price-asc' | 'price-desc')}
                className="block w-full sm:w-auto min-w-[140px] px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="default">默认排序</option>
                <option value="price-asc">价格 ↑</option>
                <option value="price-desc">价格 ↓</option>
              </select>
            </div>
          </div>

          {/* 分类筛选 */}
          <div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === '' ? 'default' : 'outline'}
                onClick={() => handleCategoryChange('')}
                size="sm"
              >
                全部商品
              </Button>
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? 'default' : 'outline'}
                  onClick={() => handleCategoryChange(category.id)}
                  size="sm"
                >
                  {category.name} ({category._count.products})
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* 搜索结果统计 */}
        {!loading && (searchTerm || selectedCategory || sortBy !== 'default') && (
          <div className="mb-6 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {searchTerm && (
                <span>搜索 "{searchTerm}" </span>
              )}
              {selectedCategory && (
                <span>在 "{categories.find(c => c.id === selectedCategory)?.name}" 分类中 </span>
              )}
              找到 <span className="font-medium text-gray-900">{filteredAndSortedProducts.length}</span> 个商品
            </div>
            {(searchTerm || selectedCategory) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('')
                  setSortBy('default')
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                清除筛选
              </Button>
            )}
          </div>
        )}

        {/* 商品列表 */}
        {loading ? (
          <div className="text-center py-16">
            <div className="text-gray-500">加载中...</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredAndSortedProducts.map((product) => (
              <Card key={product.id} className="overflow-hidden hover-simple">
                <Link href={`/product/${product.id}`}>
                  <div className="relative">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-48 object-cover"
                      />
                    ) : (
                      <div className="w-full h-48 bg-gray-100 flex items-center justify-center">
                        <Package className="w-12 h-12 text-gray-400" />
                      </div>
                    )}
                    {product._count.cards === 0 && (
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                        <span className="text-white text-sm font-medium">缺货</span>
                      </div>
                    )}
                  </div>
                </Link>

                <CardContent className="p-4">
                  <div className="text-sm text-gray-500 mb-1">{product.category.name}</div>
                  <Link href={`/product/${product.id}`}>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 hover:text-gray-700 transition-colors">
                      {product.name}
                    </h3>
                  </Link>
                  {product.description && (
                    <div className="mb-3">
                      <ContentPreview content={product.description} maxLength={120} />
                    </div>
                  )}
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-xl font-bold text-gray-900">
                      {formatPrice(product.price)}
                    </div>
                    <div className="text-sm text-gray-500">
                      库存: {product._count.cards}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Link href={`/product/${product.id}`} className="flex-1">
                      <Button variant="outline" className="w-full">
                        查看详情
                      </Button>
                    </Link>
                    <Button
                      className="flex-1"
                      disabled={product._count.cards === 0}
                      onClick={() => handleBuyNow(product.id)}
                    >
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      {product._count.cards === 0 ? '缺货' : '购买'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {filteredAndSortedProducts.length === 0 && !loading && (
          <div className="text-center py-16">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">
              {searchTerm || selectedCategory ? '未找到匹配的商品' : '暂无商品'}
            </h3>
            <p className="text-gray-500">
              {searchTerm || selectedCategory
                ? '请尝试调整搜索条件或选择其他分类'
                : '当前没有可用的商品，请稍后再来查看'
              }
            </p>
            {(searchTerm || selectedCategory) && (
              <div className="mt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedCategory('')
                    setSortBy('default')
                  }}
                >
                  清除筛选条件
                </Button>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  )
}
