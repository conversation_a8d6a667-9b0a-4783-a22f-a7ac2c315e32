'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, usePathname } from 'next/navigation'

export default function MaintenanceCheck() {
  const [isChecking, setIsChecking] = useState(true)
  const { data: session } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const checkMaintenanceMode = async () => {
      try {
        const response = await fetch('/api/admin/settings/maintenance')
        if (response.ok) {
          const data = await response.json()
          
          if (data.maintenanceMode) {
            // 允许访问的路径
            const allowedPaths = [
              '/maintenance',
              '/auth/signin',
              '/admin'
            ]
            
            // 检查当前路径是否在允许列表中
            const isAllowed = allowedPaths.some(path => pathname.startsWith(path))
            
            // 如果是管理员，允许访问所有页面
            const isAdmin = session?.user?.role === 'ADMIN'
            
            if (!isAllowed && !isAdmin) {
              router.push('/maintenance')
              return
            }
          }
        }
      } catch (error) {
        console.error('检查维护模式失败:', error)
      } finally {
        setIsChecking(false)
      }
    }

    checkMaintenanceMode()
  }, [session, pathname, router])

  // 在检查期间显示加载状态
  if (isChecking) {
    return (
      <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载...</p>
        </div>
      </div>
    )
  }

  return null
}
