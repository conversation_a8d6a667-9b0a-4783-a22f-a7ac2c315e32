import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

// 检查维护模式状态 - 使用简单的数据库查询
async function checkMaintenanceMode(): Promise<boolean> {
  try {
    // 由于Edge Runtime限制，我们使用环境变量或简单的文件检查
    // 这里我们先返回false，稍后会通过其他方式实现
    return false
  } catch (error) {
    console.error('检查维护模式失败:', error)
    return false
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 检查维护模式
  const isInMaintenance = await checkMaintenanceMode()

  if (isInMaintenance) {
    // 允许访问的路径（管理员相关和维护页面）
    const allowedPaths = [
      '/maintenance',
      '/auth/signin',
      '/api/auth',
      '/admin',
      '/api/admin',
      '/_next',
      '/favicon.ico'
    ]

    // 检查当前路径是否在允许列表中
    const isAllowed = allowedPaths.some(path => pathname.startsWith(path))

    if (!isAllowed) {
      // 对于管理员，检查是否已登录
      if (pathname.startsWith('/admin')) {
        const token = await getToken({ req: request })
        if (token && token.role === 'ADMIN') {
          // 管理员可以访问
          return NextResponse.next()
        }
      }

      // 重定向到维护页面
      return NextResponse.redirect(new URL('/maintenance', request.url))
    }
  }

  // 保护管理员路由
  if (pathname.startsWith('/admin')) {
    const token = await getToken({ req: request })

    if (!token) {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    if (token.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
