{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport CredentialsProvider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          username: user.username,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.username = user.username\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n        session.user.username = token.username as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/admin/settings/maintenance/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { promises as fs } from 'fs'\nimport path from 'path'\n\nconst MAINTENANCE_FILE = path.join(process.cwd(), 'maintenance.json')\n\ninterface MaintenanceSettings {\n  maintenanceMode: boolean\n  maintenanceMessage: string\n  updatedAt: string\n}\n\n// 读取维护设置\nasync function readMaintenanceSettings(): Promise<MaintenanceSettings> {\n  try {\n    const data = await fs.readFile(MAINTENANCE_FILE, 'utf8')\n    return JSON.parse(data)\n  } catch (error) {\n    // 如果文件不存在，返回默认设置\n    return {\n      maintenanceMode: false,\n      maintenanceMessage: '网站正在维护中，请稍后再试。',\n      updatedAt: new Date().toISOString()\n    }\n  }\n}\n\n// 写入维护设置\nasync function writeMaintenanceSettings(settings: MaintenanceSettings): Promise<void> {\n  await fs.writeFile(MAINTENANCE_FILE, JSON.stringify(settings, null, 2), 'utf8')\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user.role !== 'ADMIN') {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const settings = await readMaintenanceSettings()\n    return NextResponse.json(settings)\n\n  } catch (error) {\n    console.error('获取维护设置失败:', error)\n    return NextResponse.json({ error: '获取设置失败' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user.role !== 'ADMIN') {\n      return NextResponse.json({ error: '未授权' }, { status: 401 })\n    }\n\n    const { maintenanceMode, maintenanceMessage } = await request.json()\n\n    // 验证输入\n    if (typeof maintenanceMode !== 'boolean') {\n      return NextResponse.json({ error: '维护模式状态必须是布尔值' }, { status: 400 })\n    }\n\n    if (!maintenanceMessage || typeof maintenanceMessage !== 'string') {\n      return NextResponse.json({ error: '维护消息不能为空' }, { status: 400 })\n    }\n\n    const settings: MaintenanceSettings = {\n      maintenanceMode,\n      maintenanceMessage: maintenanceMessage.trim(),\n      updatedAt: new Date().toISOString()\n    }\n\n    await writeMaintenanceSettings(settings)\n\n    return NextResponse.json({ \n      success: true, \n      message: '维护设置保存成功',\n      settings \n    })\n\n  } catch (error) {\n    console.error('保存维护设置失败:', error)\n    return NextResponse.json({ error: '保存设置失败' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAQlD,SAAS;AACT,eAAe;IACb,IAAI;QACF,MAAM,OAAO,MAAM,6FAAA,CAAA,WAAE,CAAC,QAAQ,CAAC,kBAAkB;QACjD,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,iBAAiB;QACjB,OAAO;YACL,iBAAiB;YACjB,oBAAoB;YACpB,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;AACF;AAEA,SAAS;AACT,eAAe,yBAAyB,QAA6B;IACnE,MAAM,6FAAA,CAAA,WAAE,CAAC,SAAS,CAAC,kBAAkB,KAAK,SAAS,CAAC,UAAU,MAAM,IAAI;AAC1E;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,WAAW,MAAM;QACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAS,GAAG;YAAE,QAAQ;QAAI;IAC9D;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAM,GAAG;gBAAE,QAAQ;YAAI;QAC3D;QAEA,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElE,OAAO;QACP,IAAI,OAAO,oBAAoB,WAAW;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,IAAI,CAAC,sBAAsB,OAAO,uBAAuB,UAAU;YACjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,MAAM,WAAgC;YACpC;YACA,oBAAoB,mBAAmB,IAAI;YAC3C,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,yBAAyB;QAE/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAS,GAAG;YAAE,QAAQ;QAAI;IAC9D;AACF", "debugId": null}}]}