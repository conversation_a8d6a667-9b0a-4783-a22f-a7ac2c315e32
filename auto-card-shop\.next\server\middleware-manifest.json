{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_2ebd57a8._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_2c088799.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(.*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "W1guVrC+Px7EIJSfR0SnSL5iOnRJMCzuf2lPvgcuNgA=", "__NEXT_PREVIEW_MODE_ID": "0624ebc21f781603f1b9f0cddc5c4f14", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "85046c876a9f4273f2f0ec2c1ebf1a91007bf6b1191c0e24a276e349476c0bd0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c9478fa29f991b30f4f1c6096bf9f38435a5a2024666010215385754a9ee8fba"}}}, "sortedMiddleware": ["/"], "functions": {}}