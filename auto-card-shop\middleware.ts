import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from 'next-auth/jwt'

// 检查维护模式状态 - 暂时返回固定值进行测试
async function checkMaintenanceMode(request: NextRequest): Promise<boolean> {
  // 暂时返回 true 来测试维护模式功能
  return true
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  console.log(`[Middleware] 处理路径: ${pathname}`)

  // 跳过静态资源、API路由和维护页面的维护模式检查
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/favicon.ico') ||
    pathname === '/maintenance' ||
    pathname.startsWith('/auth')
  ) {
    // 对于管理员路由仍需要进行权限检查
    if (pathname.startsWith('/admin')) {
      const token = await getToken({ req: request })

      if (!token) {
        return NextResponse.redirect(new URL('/auth/signin', request.url))
      }

      if (token.role !== 'ADMIN') {
        return NextResponse.redirect(new URL('/', request.url))
      }
    }

    return NextResponse.next()
  }

  // 检查维护模式
  console.log(`[Middleware] 开始检查维护模式: ${pathname}`)
  const isInMaintenance = await checkMaintenanceMode(request)
  console.log(`[Middleware] 维护模式状态: ${isInMaintenance}`)

  if (isInMaintenance) {
    // 检查用户是否是管理员
    const token = await getToken({ req: request })
    const isAdmin = token && token.role === 'ADMIN'

    // 允许访问的路径
    const allowedPaths = [
      '/maintenance',
      '/auth/signin',
      '/auth/signout'
    ]

    // 如果是管理员，允许访问管理员相关路径
    if (isAdmin && pathname.startsWith('/admin')) {
      return NextResponse.next()
    }

    // 检查当前路径是否在允许列表中
    const isAllowedPath = allowedPaths.some(path => pathname.startsWith(path))

    if (!isAllowedPath && !isAdmin) {
      // 重定向到维护页面
      return NextResponse.redirect(new URL('/maintenance', request.url))
    }
  }

  // 保护管理员路由
  if (pathname.startsWith('/admin')) {
    const token = await getToken({ req: request })

    if (!token) {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    if (token.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/|api/auth).*)',
  ],
}
